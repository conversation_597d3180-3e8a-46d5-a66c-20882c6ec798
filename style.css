body {
    margin: 0;
    overflow: hidden;
    font-family: '<PERSON><PERSON><PERSON> UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

#container {
    position: relative;
    width: 100vw;
    height: 100vh;
}

#ui {
    position: absolute;
    top: 20px;
    left: 20px;
    background: rgba(255, 255, 255, 0.95);
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    z-index: 100;
    min-width: 280px;
}

.control-group {
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.control-group:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.control-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

#ui input, #ui button {
    margin: 3px;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    transition: all 0.2s ease;
}

#ui button {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    cursor: pointer;
    font-weight: 500;
}

#ui button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

#ui button:active {
    transform: translateY(0);
}

#ui button:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

#ui input[type="number"] {
    width: 60px;
    text-align: center;
}

#ui input[type="range"] {
    width: 100%;
    margin: 8px 0;
}

.layer-controls {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 5px;
    margin: 8px 0;
}

.layer-btn {
    padding: 6px 8px !important;
    font-size: 12px !important;
    min-width: 40px;
}

.info {
    font-size: 13px;
    color: #666;
}

.info div {
    margin: 4px 0;
}

#status {
    font-weight: 600;
    color: #333;
}

#layerDisplay {
    font-size: 12px;
    color: #666;
    margin-left: 8px;
}

canvas {
    display: block;
}

/* Responsive design */
@media (max-width: 768px) {
    #ui {
        top: 10px;
        left: 10px;
        right: 10px;
        padding: 15px;
        min-width: auto;
    }

    .layer-controls {
        grid-template-columns: repeat(2, 1fr);
    }
}
