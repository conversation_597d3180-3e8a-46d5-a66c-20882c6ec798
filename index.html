<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Rubik's Cube - Three.js</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://threejs.org/examples/js/controls/OrbitControls.js"></script>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div id="container">
        <div id="ui">
            <div class="control-group">
                <label for="cubeSize">Cube Size (1-20):</label>
                <input type="number" id="cubeSize" min="1" max="20" value="3">
                <button id="generateBtn">Generate Cube</button>
            </div>

            <div class="control-group">
                <button id="scrambleBtn">Scramble</button>
                <button id="solveBtn">Solve</button>
                <button id="resetBtn">Reset</button>
            </div>

            <div class="control-group">
                <label>Layer Controls:</label>
                <div class="layer-controls">
                    <button class="layer-btn" data-axis="0" data-direction="1">X+</button>
                    <button class="layer-btn" data-axis="0" data-direction="-1">X-</button>
                    <button class="layer-btn" data-axis="1" data-direction="1">Y+</button>
                    <button class="layer-btn" data-axis="1" data-direction="-1">Y-</button>
                    <button class="layer-btn" data-axis="2" data-direction="1">Z+</button>
                    <button class="layer-btn" data-axis="2" data-direction="-1">Z-</button>
                </div>
                <input type="range" id="layerSlider" min="0" max="2" value="0" step="1">
                <span id="layerDisplay">Layer: 0</span>
            </div>

            <div class="info">
                <div id="status">Ready</div>
                <div id="moveCount">Moves: 0</div>
            </div>
        </div>
    </div>
    <script src="main.js"></script>
</body>
</html>
