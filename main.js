// Global variables
let scene, camera, renderer, controls;
let cubeGroup, cubies = [];
let cubeSize = 3;
let moveHistory = [];
let isSolving = false;
let isScrambling = false;
let solvingSteps = [];
let currentLayer = 0;
let moveCount = 0;
let originalState = [];

// Colors for Rubik's Cube faces (standard)
const COLORS = {
    WHITE: 0xffffff,   // White (up)
    YELLOW: 0xffff00,  // Yellow (down)
    RED: 0xff0000,     // Red (front)
    ORANGE: 0xff7f00,  // Orange (back)
    BLUE: 0x0000ff,    // Blue (left)
    GREEN: 0x00ff00,   // Green (right)
    BLACK: 0x222222    // Internal faces
};

// Face mapping for proper cube coloring
const FACE_COLORS = [
    COLORS.GREEN,  // Right (+X)
    COLORS.BLUE,   // Left (-X)
    COLORS.WHITE,  // Top (+Y)
    COLORS.YELLOW, // Bottom (-Y)
    COLORS.RED,    // Front (+Z)
    COLORS.ORANGE  // Back (-Z)
];

// Initialize the scene
function init() {
    console.log('Initializing Rubik\'s Cube...');

    // Create scene
    scene = new THREE.Scene();
    scene.background = new THREE.Color(0x1a1a2e);

    // Create camera
    camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    updateCameraPosition();

    // Create renderer
    renderer = new THREE.WebGLRenderer({ antialias: true });
    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.shadowMap.enabled = true;
    renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    document.getElementById('container').appendChild(renderer.domElement);

    // Add orbit controls
    controls = new THREE.OrbitControls(camera, renderer.domElement);
    controls.enableDamping = true;
    controls.dampingFactor = 0.05;
    controls.minDistance = 5;
    controls.maxDistance = 100;

    // Add lights
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.4);
    scene.add(ambientLight);

    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(10, 10, 10);
    directionalLight.castShadow = true;
    directionalLight.shadow.mapSize.width = 2048;
    directionalLight.shadow.mapSize.height = 2048;
    scene.add(directionalLight);

    // Add additional lights for better illumination
    const light2 = new THREE.DirectionalLight(0xffffff, 0.3);
    light2.position.set(-10, -10, -10);
    scene.add(light2);

    // Create the cube
    generateCube(cubeSize);
    console.log(`Generated ${cubeSize}x${cubeSize}x${cubeSize} cube with ${cubies.length} visible cubies`);

    // Event listeners
    setupEventListeners();

    // Start animation loop
    animate();
    console.log('Rubik\'s Cube initialized successfully!');
}

// Update camera position based on cube size
function updateCameraPosition() {
    const distance = Math.max(cubeSize * 3, 8);
    camera.position.set(distance, distance, distance);
    camera.lookAt(0, 0, 0);
}

// Setup all event listeners
function setupEventListeners() {
    window.addEventListener('resize', onWindowResize);

    // Cube generation
    document.getElementById('generateBtn').addEventListener('click', () => {
        const newSize = parseInt(document.getElementById('cubeSize').value);
        if (newSize >= 1 && newSize <= 20) {
            cubeSize = newSize;
            generateCube(cubeSize);
            updateLayerSlider();
            updateCameraPosition();
        }
    });

    // Control buttons
    document.getElementById('solveBtn').addEventListener('click', startSolving);
    document.getElementById('scrambleBtn').addEventListener('click', scrambleCube);
    document.getElementById('resetBtn').addEventListener('click', resetCube);

    // Layer controls
    document.getElementById('layerSlider').addEventListener('input', (e) => {
        currentLayer = parseInt(e.target.value);
        updateLayerDisplay();
    });

    // Layer rotation buttons
    document.querySelectorAll('.layer-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
            const axis = parseInt(e.target.dataset.axis);
            const direction = parseInt(e.target.dataset.direction);
            rotateLayer(axis, currentLayer, direction);
        });
    });

    // Mouse interaction
    renderer.domElement.addEventListener('mousedown', onMouseDown);
    renderer.domElement.addEventListener('mousemove', onMouseMove);
    renderer.domElement.addEventListener('mouseup', onMouseUp);
    renderer.domElement.addEventListener('contextmenu', (e) => e.preventDefault());
}

// Generate a Rubik's Cube of given size
function generateCube(size) {
    // Clear existing cube
    if (cubeGroup) scene.remove(cubeGroup);
    cubeGroup = new THREE.Group();
    cubies = [];
    moveHistory = [];
    moveCount = 0;
    originalState = [];

    // Create cubies
    const cubieSize = 0.95;
    const spacing = 1;
    const offset = (size - 1) * spacing / 2;

    for (let x = 0; x < size; x++) {
        for (let y = 0; y < size; y++) {
            for (let z = 0; z < size; z++) {
                // Skip internal cubies that aren't visible
                if (x !== 0 && x !== size - 1 &&
                    y !== 0 && y !== size - 1 &&
                    z !== 0 && z !== size - 1) continue;

                const cubie = createCubie(cubieSize, x, y, z, size);
                cubie.position.set(
                    x * spacing - offset,
                    y * spacing - offset,
                    z * spacing - offset
                );
                cubie.userData.gridPosition = { x, y, z };
                cubeGroup.add(cubie);
                cubies.push(cubie);
            }
        }
    }

    scene.add(cubeGroup);

    // Store original state for solving
    storeOriginalState();
    updateMoveCount();
    updateStatus('Ready');
}

// Create a single cubie
function createCubie(size, x, y, z, cubeSize) {
    const geometry = new THREE.BoxGeometry(size, size, size);
    const materials = [];

    // Determine which faces are visible (on the surface of the cube)
    const isRightFace = x === cubeSize - 1;
    const isLeftFace = x === 0;
    const isTopFace = y === cubeSize - 1;
    const isBottomFace = y === 0;
    const isFrontFace = z === cubeSize - 1;
    const isBackFace = z === 0;

    // Create materials for each face (Three.js face order: right, left, top, bottom, front, back)
    materials.push(new THREE.MeshStandardMaterial({
        color: isRightFace ? COLORS.GREEN : COLORS.BLACK
    })); // Right
    materials.push(new THREE.MeshStandardMaterial({
        color: isLeftFace ? COLORS.BLUE : COLORS.BLACK
    })); // Left
    materials.push(new THREE.MeshStandardMaterial({
        color: isTopFace ? COLORS.WHITE : COLORS.BLACK
    })); // Top
    materials.push(new THREE.MeshStandardMaterial({
        color: isBottomFace ? COLORS.YELLOW : COLORS.BLACK
    })); // Bottom
    materials.push(new THREE.MeshStandardMaterial({
        color: isFrontFace ? COLORS.RED : COLORS.BLACK
    })); // Front
    materials.push(new THREE.MeshStandardMaterial({
        color: isBackFace ? COLORS.ORANGE : COLORS.BLACK
    })); // Back

    // Set material properties
    materials.forEach(material => {
        material.metalness = 0.1;
        material.roughness = 0.3;
    });

    const cubie = new THREE.Mesh(geometry, materials);
    cubie.userData = {
        originalPos: new THREE.Vector3(x, y, z),
        originalColors: materials.map(m => m.color.getHex())
    };
    cubie.castShadow = true;
    cubie.receiveShadow = true;

    return cubie;
}

// Mouse interaction variables
let isDragging = false;
let startPosition = new THREE.Vector2();
let endPosition = new THREE.Vector2();
let raycaster = new THREE.Raycaster();

function onMouseDown(event) {
    if (isSolving || isScrambling) return;
    isDragging = true;
    startPosition.set(
        (event.clientX / window.innerWidth) * 2 - 1,
        -(event.clientY / window.innerHeight) * 2 + 1
    );
}

function onMouseMove(event) {
    if (!isDragging || isSolving || isScrambling) return;

    endPosition.set(
        (event.clientX / window.innerWidth) * 2 - 1,
        -(event.clientY / window.innerHeight) * 2 + 1
    );
}

function onMouseUp() {
    if (!isDragging || isSolving || isScrambling) return;
    isDragging = false;

    // Calculate drag direction
    const delta = new THREE.Vector2().subVectors(endPosition, startPosition);
    if (delta.length() < 0.05) return; // Ignore small drags

    // Cast ray to find selected cubie
    raycaster.setFromCamera(startPosition, camera);
    const intersects = raycaster.intersectObjects(cubies);
    if (intersects.length === 0) return;

    const face = getClickedFace(intersects[0].faceIndex, intersects[0].object);
    if (!face) return;

    // Determine rotation direction based on drag
    let direction = 1;
    if (Math.abs(delta.x) > Math.abs(delta.y)) {
        direction = delta.x > 0 ? 1 : -1;
    } else {
        direction = delta.y > 0 ? -1 : 1;
    }

    rotateLayer(face.axis, face.index, direction);
}

// Get the face that was clicked
function getClickedFace(faceIndex, cubie) {
    // Face indices in Three.js: 
    // 0: right, 1: left, 2: top, 3: bottom, 4: front, 5: back
    const axis = Math.floor(faceIndex / 2); // 0: x, 1: y, 2: z
    const direction = faceIndex % 2 === 0 ? 1 : -1;
    
    // Get the position of the cubie to determine which layer it's on
    const pos = cubie.position;
    const offset = (cubeSize - 1) / 2;
    let index;
    
    if (axis === 0) index = Math.round(pos.x + offset);
    else if (axis === 1) index = Math.round(pos.y + offset);
    else index = Math.round(pos.z + offset);
    
    return { axis, index, direction };
}

// Rotate a layer of the cube
function rotateLayer(axis, index, direction) {
    if (isSolving || isScrambling) return;

    // Collect all cubies in the layer
    const layerCubies = [];
    const offset = (cubeSize - 1) / 2;

    cubies.forEach(cubie => {
        const pos = cubie.position;
        let inLayer = false;

        if (axis === 0 && Math.round(pos.x + offset) === index) inLayer = true;
        else if (axis === 1 && Math.round(pos.y + offset) === index) inLayer = true;
        else if (axis === 2 && Math.round(pos.z + offset) === index) inLayer = true;

        if (inLayer) layerCubies.push(cubie);
    });

    if (layerCubies.length === 0) return;

    // Create a group for the layer and rotate it
    const layerGroup = new THREE.Group();
    layerCubies.forEach(cubie => {
        cubeGroup.remove(cubie);
        layerGroup.add(cubie);
    });
    scene.add(layerGroup);

    const rotationAxis = new THREE.Vector3();
    rotationAxis.set(axis === 0 ? 1 : 0, axis === 1 ? 1 : 0, axis === 2 ? 1 : 0);
    const angle = (Math.PI / 2) * direction;

    // Animate the rotation
    const duration = isScrambling ? 150 : 300; // Faster for scrambling
    const startTime = Date.now();

    function animateRotation() {
        const elapsed = Date.now() - startTime;
        const progress = Math.min(elapsed / duration, 1);
        const currentAngle = angle * progress;

        layerGroup.rotation.set(
            rotationAxis.x * currentAngle,
            rotationAxis.y * currentAngle,
            rotationAxis.z * currentAngle
        );

        if (progress < 1) {
            requestAnimationFrame(animateRotation);
        } else {
            // Finalize rotation
            layerCubies.forEach(cubie => {
                layerGroup.remove(cubie);

                // Update cubie position
                const matrix = new THREE.Matrix4().makeRotationAxis(rotationAxis, angle);
                cubie.position.applyMatrix4(matrix);
                cubie.rotation.set(0, 0, 0);

                cubeGroup.add(cubie);
            });
            scene.remove(layerGroup);

            // Record move (only if not scrambling or solving)
            if (!isScrambling && !isSolving) {
                moveHistory.push({ axis, index, direction });
                moveCount++;
                updateMoveCount();
            }
        }
    }

    animateRotation();
}

// Start solving the cube
function startSolving() {
    if (isSolving || moveHistory.length === 0) return;
    isSolving = true;
    updateStatus('Solving...');

    // Reverse move history for solving
    solvingSteps = [...moveHistory].reverse();
    moveHistory = [];

    // Execute solving steps one by one
    executeNextSolvingStep();
}

// Execute the next solving step
function executeNextSolvingStep() {
    if (solvingSteps.length === 0) {
        isSolving = false;
        updateStatus('Solved!');
        return;
    }

    const step = solvingSteps.shift();

    // Temporarily allow rotation during solving
    const wasSolving = isSolving;
    isSolving = false;
    rotateLayer(step.axis, step.index, -step.direction);
    isSolving = wasSolving;

    // Wait for rotation to complete
    setTimeout(executeNextSolvingStep, 400);
}

// Scramble the cube
function scrambleCube() {
    if (isSolving || isScrambling) return;
    isScrambling = true;
    updateStatus('Scrambling...');

    const scrambleMoves = Math.min(cubeSize * 10, 50); // More moves for larger cubes
    let movesLeft = scrambleMoves;

    function executeScrambleMove() {
        if (movesLeft <= 0) {
            isScrambling = false;
            updateStatus('Scrambled - Ready to solve!');
            return;
        }

        const axis = Math.floor(Math.random() * 3);
        const index = Math.floor(Math.random() * cubeSize);
        const direction = Math.random() < 0.5 ? 1 : -1;

        // Temporarily allow rotation during scrambling
        const wasScrambling = isScrambling;
        isScrambling = false;
        rotateLayer(axis, index, direction);
        isScrambling = wasScrambling;

        movesLeft--;
        setTimeout(executeScrambleMove, 200);
    }

    executeScrambleMove();
}

// Reset cube to original state
function resetCube() {
    if (isSolving || isScrambling) return;
    generateCube(cubeSize);
}

// Store original state for solving
function storeOriginalState() {
    originalState = cubies.map(cubie => ({
        position: cubie.position.clone(),
        colors: cubie.userData.originalColors.slice()
    }));
}

// Update layer slider based on cube size
function updateLayerSlider() {
    const slider = document.getElementById('layerSlider');
    slider.max = cubeSize - 1;
    slider.value = Math.min(currentLayer, cubeSize - 1);
    currentLayer = parseInt(slider.value);
    updateLayerDisplay();
}

// Update layer display
function updateLayerDisplay() {
    document.getElementById('layerDisplay').textContent = `Layer: ${currentLayer}`;
}

// Update move count display
function updateMoveCount() {
    document.getElementById('moveCount').textContent = `Moves: ${moveCount}`;
}

// Update status display
function updateStatus(status) {
    document.getElementById('status').textContent = status;
}

// Handle window resize
function onWindowResize() {
    camera.aspect = window.innerWidth / window.innerHeight;
    camera.updateProjectionMatrix();
    renderer.setSize(window.innerWidth, window.innerHeight);
}

// Animation loop
function animate() {
    requestAnimationFrame(animate);
    controls.update();
    renderer.render(scene, camera);
}

// Initialize the application
window.addEventListener('load', init);
